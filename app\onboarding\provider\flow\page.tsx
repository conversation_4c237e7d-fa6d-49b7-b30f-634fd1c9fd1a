"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, ChefHat, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui/typography";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useUser, useIsProvider } from "@/hooks/use-auth";
import {
  useCreateProvider,
  useProviderStatus,
  type ProviderOnboardingData,
} from "@/hooks/use-provider-onboarding";

// Simplified validation schema for essential fields only
const simplifiedOnboardingSchema = z.object({
  businessName: z
    .string()
    .min(2, "Business name must be at least 2 characters"),
  description: z
    .string()
    .min(10, "Description must be at least 10 characters")
    .max(500, "Description must be less than 500 characters"),
  contactPersonName: z
    .string()
    .min(2, "Contact person name must be at least 2 characters"),
  mobileNumber: z.string().min(1, "Mobile number is required"),
  serviceAreas: z.string().min(1, "Service areas are required"),
});

type SimplifiedFormData = z.infer<typeof simplifiedOnboardingSchema>;

export default function ProviderOnboardingFlowPage() {
  const router = useRouter();
  const { data: user, isLoading: isUserLoading } = useUser();
  const { value: isProvider } = useIsProvider();

  // TanStack Query hooks
  const createProviderMutation = useCreateProvider();
  const { data: isExistingProvider, isLoading: isCheckingProvider } =
    useProviderStatus();

  // Simplified form with essential fields only
  const form = useForm<SimplifiedFormData>({
    resolver: zodResolver(simplifiedOnboardingSchema),
    defaultValues: {
      businessName: "",
      description: "",
      contactPersonName: "",
      mobileNumber: "",
      serviceAreas: "",
    },
    mode: "onChange",
  });

  // Navigation state
  const [isRedirecting, setIsRedirecting] = React.useState(false);
  const redirectTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Convert simplified form data to full onboarding data format
  const convertToOnboardingData = (
    data: SimplifiedFormData
  ): ProviderOnboardingData => {
    return {
      businessName: data.businessName,
      businessAddress: "", // Optional field, set to empty
      description: data.description,
      serviceAreas: data.serviceAreas
        .split(",")
        .map((area) => area.trim())
        .filter(Boolean),
      contactPersonName: data.contactPersonName,
      mobileNumber: data.mobileNumber,
      socialMediaLinks: {}, // Optional field, set to empty object
      // File uploads removed for simplicity
      logo: undefined,
      sampleMenu: undefined,
    };
  };

  // Optimized redirect logic with state management
  React.useEffect(() => {
    // Clear any existing redirect timeout
    if (redirectTimeoutRef.current) {
      clearTimeout(redirectTimeoutRef.current);
    }

    // Don't redirect if already redirecting or still loading
    if (isRedirecting || isUserLoading || isCheckingProvider) {
      return;
    }

    // Redirect if not logged in
    if (!user) {
      setIsRedirecting(true);
      redirectTimeoutRef.current = setTimeout(() => {
        router.push(
          "/login?redirect=" + encodeURIComponent("/onboarding/provider/flow")
        );
      }, 100); // Small delay to prevent flash
      return;
    }

    // Redirect if already a provider
    if (isProvider || isExistingProvider) {
      setIsRedirecting(true);
      redirectTimeoutRef.current = setTimeout(() => {
        router.push("/dashboard");
      }, 100); // Small delay to prevent flash
      return;
    }
  }, [
    user,
    isUserLoading,
    isProvider,
    isExistingProvider,
    isCheckingProvider,
    router,
    isRedirecting,
  ]);

  // Cleanup redirect timeout on unmount
  React.useEffect(() => {
    return () => {
      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
      }
    };
  }, []);

  // Show simple loading state
  if (isUserLoading || isCheckingProvider || isRedirecting) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  // Don't render if user is not logged in (will redirect)
  if (!user) {
    return null;
  }

  // Handle form submission
  const handleSubmit = async (data: SimplifiedFormData) => {
    // Check if user is already a provider
    if (isExistingProvider) {
      toast.error("You are already a catering provider.");
      router.push("/dashboard");
      return;
    }

    // Convert simplified form data to full onboarding data format
    const onboardingData = convertToOnboardingData(data);

    // Submit using TanStack Query mutation
    createProviderMutation.mutate(onboardingData, {
      onSuccess: () => {
        toast.success(
          "🎉 Onboarding completed successfully! Welcome to CateringHub!"
        );

        // Redirect to dashboard
        setIsRedirecting(true);
        redirectTimeoutRef.current = setTimeout(() => {
          router.push("/dashboard");
        }, 1000);
      },
      onError: (error) => {
        console.error("Error submitting onboarding:", error);
        toast.error("Failed to complete onboarding. Please try again.");
      },
    });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ChefHat className="h-6 w-6" />
            <Typography variant="h5">CateringHub</Typography>
          </div>

          <Button variant="ghost" asChild>
            <Link
              href="/onboarding/provider"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Link>
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="w-full max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Provider Onboarding</CardTitle>
              <CardDescription>
                Complete your catering provider profile to start accepting
                bookings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(handleSubmit)}
                  className="space-y-6"
                >
                  {/* Business Name */}
                  <FormField
                    control={form.control}
                    name="businessName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Business Name{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your catering business name"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          This will be displayed to customers when they view
                          your services.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Service Description */}
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Service Description{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe your catering services, specialties, and what makes your business unique..."
                            className="min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Tell potential customers about your catering services.
                          Include your specialties, cuisine types, and what
                          makes your business special. (10-500 characters)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Service Areas */}
                  <FormField
                    control={form.control}
                    name="serviceAreas"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Service Areas{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Manila, Quezon City, Makati (separate with commas)"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          List the areas where you provide catering services,
                          separated by commas.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Contact Person Name */}
                  <FormField
                    control={form.control}
                    name="contactPersonName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Contact Person Name{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter the name of the main contact person"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The name of the person customers should contact for
                          inquiries and bookings.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Mobile Number */}
                  <FormField
                    control={form.control}
                    name="mobileNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Mobile Number{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your mobile number (e.g., +63 ************)"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Your primary contact number for customer inquiries and
                          bookings.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Submit Button */}
                  <div className="flex justify-end pt-4">
                    <Button
                      type="submit"
                      disabled={createProviderMutation.isPending}
                      className="min-w-[120px]"
                    >
                      {createProviderMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        "Complete Onboarding"
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
